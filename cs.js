const fs = require("fs").promises;
const path = require("path");

const SKINS_FILE = path.resolve(__dirname, "skins.json");
const PRICES_FILE = path.resolve(__dirname, "prices.json");
const README_FILE = path.resolve(__dirname, "README.md");

const SAMPLES = 10;
const RARITY_ORDER = [
    "Consumer Grade",
    "Industrial Grade",
    "Mil-Spec Grade",
    "Restricted",
    "Classified",
    "Covert"
];
const FLOATS = {
    "Factory New": [0.00, 0.07],
    "Minimal Wear": [0.07, 0.15],
    "Field-Tested": [0.15, 0.38],
    "Well-Worn": [0.38, 0.45],
    "Battle-Scarred": [0.45, 1.00]
}

async function loadJson(filePath) {
    const data = await fs.readFile(filePath, "utf-8");
    return JSON.parse(data);
}



async function main() {
    const skins = await loadJson(SKINS_FILE);
    const useable_skins = skins.filter(skin => { return skin.rarity.name !== 'Covert' });
    const prices = (await loadJson(PRICES_FILE)).data;

    for(let i = 0; i < SAMPLES; i++) {

        const contract = [];

        const skin = getRandomSkin(useable_skins);
        contract.push(skin);

        for (let x = 0; x < 9; x++) {
            contract.push(getRandomSkin(useable_skins, skin.rarity.name));
        }
        console.log(contract);


        return;
    }

    // console.log(pricesRaw.data[521]);
    return;

}

function getRandomSkin(skins, rarity = null){
    if(rarity){
        skins = skins.filter(skin => { return skin.rarity.name === rarity });
    }

    const skin = skins[Math.floor(Math.random() * skins.length)];
    const wear = skin.wears[Math.floor(Math.random() * skin.wears.length)].name;
    const float = (FLOATS[wear][0] + FLOATS[wear][1]) / 2;

    return {
        name: skin.name,
        wear: wear,
        rarity: skin.rarity.name,
        collection: skin.collections[0].name,
        min_float: skin.min_float,
        max_float: skin.max_float,
        float: getFloatForWear(skin, skin.rarity.name),
    }

}


main().catch(console.error);
