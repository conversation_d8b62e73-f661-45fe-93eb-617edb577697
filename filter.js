const fs = require("fs");

const skins = JSON.parse(fs.readFileSync("skins.json", "utf8"));

// Trade-up eligible rarities
const tradeUpRarities = [
  "Consumer Grade",
  "Industrial Grade",
  "Mil-Spec Grade",
  "Restricted",
  "Classified",
  "Covert"
];

const tradeUpSkins = skins.filter(skin => {
  const rarityName = skin.rarity?.name || "";
  const categoryName = skin.category?.name?.toLowerCase() || "";

  // Exclude non-weapon categories
  const isWeapon = !["gloves", "agent", "music kit", "sticker", "patch"].some(word =>
      categoryName.includes(word)
  );

  // Must have a valid rarity for trade-ups
  const hasValidRarity = tradeUpRarities.includes(rarityName);

  // Must be part of at least one collection
  const hasCollection = Array.isArray(skin.collections) && skin.collections.length > 0;

  // Must not be a souvenir skin
  const notSouvenir = !skin.souvenir;

  return isWeapon && hasValidRarity && hasCollection && notSouvenir;
});
tradeUpSkins.forEach(skin => {
  delete skin.image;
  delete skin.team;
  delete skin.pattern;
  delete skin.category;
});

fs.writeFileSync("skins2.json", JSON.stringify(tradeUpSkins, null, 2));
console.log(`Saved ${tradeUpSkins.length} trade-up eligible skins to skins2.json`);
